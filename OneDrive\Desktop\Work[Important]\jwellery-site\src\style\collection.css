/* Collection Component Styles */

/* Ensure proper responsive behavior */
.desktop-layout {
  display: none;
}

.mobile-layout {
  display: block;
}

@media (min-width: 768px) {
  .desktop-layout {
    display: block !important;
  }

  .mobile-layout {
    display: none !important;
  }
}

.collections {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
  background-color: #ffffff;
}

#header {
  text-align: center;
  font-size: 3rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 2px;
  background: linear-gradient(45deg, #d4af37, #ffd700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

#subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 50px;
  font-weight: 300;
  letter-spacing: 1px;
}

.parent {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(5, 1fr);
  gap: 15px;
  height: 700px;
  margin-bottom: 40px;
}

.div1, .div2, .div3, .div4, .div5 {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.div1:hover, .div2:hover, .div3:hover, .div4:hover, .div5:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.div1 {
  grid-column: span 2 / span 2;
  grid-row: span 5 / span 5;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.div2 {
  grid-column: span 2 / span 2;
  grid-row: span 2 / span 2;
  grid-column-start: 3;
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
}

.div3 {
  grid-column: span 2 / span 2;
  grid-row: span 3 / span 3;
  grid-column-start: 5;
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
}

.div4 {
  grid-column: span 2 / span 2;
  grid-row: span 3 / span 3;
  grid-column-start: 3;
  grid-row-start: 3;
  background: linear-gradient(45deg, #43e97b 0%, #38f9d7 100%);
}

.div5 {
  grid-column: span 2 / span 2;
  grid-row: span 2 / span 2;
  grid-column-start: 5;
  grid-row-start: 4;
  background: linear-gradient(45deg, #fa709a 0%, #fee140 100%);
}

.div1 img, .div2 img, .div3 img, .div4 img, .div5 img {
  width: 100%;
  height: 100%;
  object-fit: fill;
  object-position: center;
  transition: transform 0.3s ease;
}

.div1 a, .div2 a, .div3 a, .div4 a, .div5 a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
}

/* Bootstrap utility class for border radius */
.br-12 {
  border-radius: 12px !important;
}

.div1:hover img, .div2:hover img, .div3:hover img, .div4:hover img, .div5:hover img {
  transform: scale(1.05);
}

/* Overlay effect */
.div1::before, .div2::before, .div3::before, .div4::before, .div5::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.div1:hover::before, .div2:hover::before, .div3:hover::before, .div4:hover::before, .div5:hover::before {
  opacity: 1;
}

/* Responsive Design with Bootstrap */
@media (max-width: 1200px) {
  .parent {
    height: 600px;
    gap: 12px;
  }

  #header {
    font-size: 2.5rem;
  }
}

/* Mobile specific styles */
@media (max-width: 767.98px) {
  #header {
    font-size: 2rem;
  }

  #subtitle {
    font-size: 1rem;
    margin-bottom: 30px;
  }

  .collections {
    padding: 20px 10px;
  }

  /* Mobile image containers */
  .mobile-layout .position-relative {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    margin-bottom: 16px;
    height: 280px;
    background: #f8f9fa;
  }

  .mobile-layout .position-relative:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  }

  .mobile-layout .position-relative img {
    width: 100%;
    height: 100%;
    object-fit: fill;
    object-position: center;
    transition: transform 0.3s ease;
  }

  .mobile-layout .position-relative:hover img {
    transform: scale(1.02);
  }

  /* First image - larger featured image */
  .mobile-layout .position-relative:first-child {
    height: 320px;
    margin-bottom: 20px;
  }

  /* Remove margin from last item */
  .mobile-layout .position-relative:last-child {
    margin-bottom: 0;
  }

  /* Mobile layout spacing */
  .mobile-layout {
    padding: 0 16px;
  }

  /* Mobile collection link */
  .mobile-collection-link {
    display: block;
    text-decoration: none;
    color: inherit;
    position: relative;
    width: 100%;
    height: 100%;
  }

  /* Mobile text overlay */
  .mobile-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: 40px 20px 20px;
    color: white;
    z-index: 2;
  }

  .mobile-text-content {
    text-align: left;
  }

  .mobile-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
  }

  .mobile-subtitle {
    font-size: 0.9rem;
    font-weight: 300;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1.3;
  }

  /* Hover effect for mobile */
  .mobile-collection-link:hover .mobile-overlay {
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  }

  .mobile-collection-link:hover .mobile-title {
    color: #ffd700;
    transition: color 0.3s ease;
  }
}