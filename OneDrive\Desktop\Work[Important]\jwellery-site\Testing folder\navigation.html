<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="index.css">
    <title>Navigation</title>
</head>
<body>
    <div class='navigation'>
        <div class='logo'>
            <h1><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON></h1>
        </div>
        <div class='search-bar'>
            <input type="text" placeholder="search for products, brands, and more..." id="search-input" onkeypress="handleSearch(event)" />
            <img type="icon" src="image1.png" onclick="performSearch()">
        </div>
        <div class="custom-image">
            <img src="image2.png" alt="Custom Image" id="diamond">
            <img src="image3.png" alt="Custom Image" id="shop">
            <img src="image4.png" alt="Custom Image" id="wishlist">
            <div class="login-dropdown">
                <img src="image5.png" alt="Custom Image" id="login-image">
                <div class="dropdown-content">
                    <a href="#" id="login">Login/sign up</a>
                    <a href="#" id="contact">Contact us</a>
                </div>
            </div>
        </div>


    </div>

    <script>
        function handleSearch(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        function performSearch() {
            const searchInput = document.getElementById('search-input');
            const searchTerm = searchInput.value.trim();

            if (searchTerm) {
                // You can modify this to redirect to your search page
                alert('Searching for: ' + searchTerm);
                // Example: window.location.href = '/search?q=' + encodeURIComponent(searchTerm);
            } else {
                alert('Please enter a search term');
            }
        }



        // Handle scroll behavior for search bar
        let lastScrollTop = 0;
        window.addEventListener('scroll', function() {
            const searchBar = document.querySelector('.search-bar');
            const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

            if (window.innerWidth <= 768) {
                if (currentScroll > 100) {
                    searchBar.classList.add('scrolled');
                } else {
                    searchBar.classList.remove('scrolled');
                }
            }

            lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const searchBar = document.querySelector('.search-bar');

            if (window.innerWidth > 768) {
                searchBar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>