/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8d697b304b401681-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/ba015fad6dcf6784-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/569ce4b8f30dc480-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Fallback';src: local("Arial");ascent-override: 95.94%;descent-override: 28.16%;line-gap-override: 0.00%;size-adjust: 104.76%
}.__className_5cfdac {font-family: 'Geist', 'Geist Fallback';font-style: normal
}.__variable_5cfdac {--font-geist-sans: 'Geist', 'Geist Fallback'
}

/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/9610d9e46709d722-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* latin-ext */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/747892c23ea88013-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Geist Mono';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/93f479601ee12b01-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Geist Mono Fallback';src: local("Arial");ascent-override: 74.67%;descent-override: 21.92%;line-gap-override: 0.00%;size-adjust: 134.59%
}.__className_9a8899 {font-family: 'Geist Mono', 'Geist Mono Fallback';font-style: normal
}.__variable_9a8899 {--font-geist-mono: 'Geist Mono', 'Geist Mono Fallback'
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* Global Responsive Foundation */
:root {
  /* Responsive Breakpoints */
  --mobile-max: 767px;
  --tablet-min: 768px;
  --tablet-max: 1023px;
  --desktop-min: 1024px;
  --large-desktop-min: 1440px;

  /* Responsive Spacing */
  --spacing-xs: clamp(0.25rem, 1vw, 0.5rem);
  --spacing-sm: clamp(0.5rem, 2vw, 1rem);
  --spacing-md: clamp(1rem, 3vw, 1.5rem);
  --spacing-lg: clamp(1.5rem, 4vw, 2rem);
  --spacing-xl: clamp(2rem, 5vw, 3rem);

  /* Responsive Typography */
  --font-size-xs: clamp(0.75rem, 2vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 2.5vw, 1rem);
  --font-size-base: clamp(1rem, 3vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 3.5vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 4vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 5vw, 2rem);
  --font-size-3xl: clamp(2rem, 6vw, 3rem);
}

html {
  background-color: transparent;
  z-index: -1;
  /* Ensure proper scaling on all devices */
  text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

body {
  max-width: 100vw;
  overflow-x: hidden;
  background-color: transparent;
  z-index: -1;
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Responsive line height */
  line-height: 1.6;
  /* Prevent horizontal scroll on mobile */
  position: relative;
  min-height: 100vh;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Responsive Typography */
h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  margin-bottom: var(--spacing-sm);
}

p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Responsive Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Responsive Grid System */
.grid {
  display: grid;
  grid-gap: var(--spacing-md);
  gap: var(--spacing-md);
}

.flex {
  display: flex;
  gap: var(--spacing-sm);
}

/* Mobile-first responsive utilities */
.mobile-only {
  display: block;
}

.tablet-only,
.desktop-only {
  display: none;
}

/* Tablet breakpoint */
@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }

  .tablet-only {
    display: block;
  }

  .tablet-up {
    display: block;
  }
}

/* Desktop breakpoint */
@media (min-width: 1024px) {
  .tablet-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }

  .desktop-up {
    display: block;
  }
}

/* Responsive text alignment */
.text-center-mobile {
  text-align: center;
}

@media (min-width: 768px) {
  .text-center-mobile {
    text-align: left;
  }
}

/* Prevent layout shift */
.no-layout-shift {
  contain: layout style paint;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/navigation.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Navigation container - Mobile First Approach */
.navigation {
    background: rgb(255, 255, 255);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1000;
    min-height: 70px;
    width: 100%;
    /* Prevent horizontal overflow */
    overflow-x: hidden;
}

/* Logo section - Responsive */
.logo h1 {
    color: rgb(196, 149, 6);
    font-size: var(--font-size-xl);
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0;
    /* Prevent text from wrapping */
    white-space: nowrap;
}

.logo h1:hover {
    transform: scale(1.05);
}

/* Logo responsive adjustments */
@media (max-width: 480px) {
    .logo h1 {
        font-size: clamp(1rem, 5vw, 1.5rem);
    }
}

/* Search bar section - Responsive */
.search-bar {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: var(--spacing-xs);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1 1;
    max-width: 600px;
    height: clamp(40px, 8vw, 50px);
    margin: 0 var(--spacing-sm);
    border: 1px solid rgb(168, 168, 168);
    transition: all 0.3s ease;
    /* Prevent overflow on small screens */
    min-width: 200px;
}

.search-bar:focus-within {
    border-color: rgb(196, 149, 6);
    box-shadow: 0 4px 15px rgba(196, 149, 6, 0.2);
}

.search-bar input {
    flex: 1 1;
    border: none;
    outline: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    border-radius: 20px;
    background: transparent;
    /* Prevent zoom on iOS */
    font-size: 16px;
    color: #333;
}

.search-bar input::placeholder {
    color: #999;
    font-style: italic;
    font-size: var(--font-size-xs);
}

.search-bar img {
    width: clamp(18px, 4vw, 24px);
    height: clamp(18px, 4vw, 24px);
    margin: 0 var(--spacing-xs);
    cursor: pointer;
    transition: transform 0.2s ease;
    /* Ensure touch target is large enough */
    min-width: 24px;
    min-height: 24px;
}

.search-bar img:hover {
    transform: scale(1.1);
}

/* Search bar mobile adjustments */
@media (max-width: 480px) {
    .search-bar {
        min-width: 150px;
        margin: 0 var(--spacing-xs);
        height: 40px;
    }

    .search-bar input {
        font-size: 14px;
        padding: var(--spacing-xs);
    }

    .search-bar input::placeholder {
        font-size: 12px;
    }
}

/* Scrolled search bar styles */
.search-bar.scrolled {
    position: fixed;
    top: 1rem;
    right: 1rem;
    width: 50px !important;
    height: 50px !important;
    max-width: 50px !important;
    border-radius: 50% !important;
    padding: 0 !important;
    z-index: 1003;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
}

.search-bar.scrolled input {
    display: none;
}

.search-bar.scrolled img {
    margin: 0;
    width: 24px;
    height: 24px;
}

/* Search button styles removed - now using Enter key and icon click */

/* Custom image section */
.custom-image {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.custom-image img {
    width: clamp(24px, 3vw, 32px);
    height: clamp(24px, 3vw, 32px);
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
    min-height: 44px; /* Touch-friendly minimum size */
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-image img:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0);
}

/* Login dropdown section */
.login-dropdown {
    position: relative;
    display: inline-block;
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(0, 0, 0, 0.3);
}

.login-dropdown img {
    width: 32px;
    height: 32px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
}

.login-dropdown img:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Dropdown content */
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background: rgb(255, 255, 255);
    min-width: 180px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    z-index: 9999;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-content::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgb(255, 255, 255);
}

.login-dropdown:hover .dropdown-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-content a {
    color: #000000;
    padding: 0.75rem 1.25rem;
    text-decoration: none;
    display: block;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    border-radius: 0;
}

.dropdown-content a:hover {
    background: #832729;
    color: rgb(255, 255, 255);
    transform: translateX(5px);
}

.dropdown-content a:first-child {
    border-radius: 12px 12px 0 0;
}

.dropdown-content a:last-child {
    border-radius: 0 0 12px 12px;
}

/* Removed mobile menu styles - using direct icon display instead */

/* Responsive design */
@media (max-width: 1024px) {
    .navigation {
        padding: 1rem 1.5rem;
    }

    .search-bar {
        max-width: 450px;
    }
}

@media (max-width: 768px) {
    .navigation {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
        gap: 1rem;
        min-height: auto;
    }

    /* Top row: Logo left, Custom images right */
    .navigation::before {
        content: '';
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        order: 1;
    }

    .logo {
        order: 1;
        align-self: flex-start;
        position: absolute;
        top: 1rem;
        left: 1rem;
        z-index: 10;
    }

    .custom-image {
        order: 2;
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
        z-index: 10;
    }

    .custom-image img {
        width: 28px;
        height: 28px;
        min-width: 36px;
        min-height: 36px;
    }

    /* Search bar below */
    .search-bar {
        order: 3;
        margin-top: 3rem; /* Space for top row */
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
        height: 45px;
    }

    .search-bar input {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* When scrolled, search bar becomes floating icon */
    .search-bar.scrolled {
        position: fixed;
        top: 1rem;
        right: 1rem;
        margin-top: 0;
        z-index: 1003;
    }

    /* Login dropdown positioning */
    .login-dropdown {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
        position: relative;
    }

    .dropdown-content {
        right: 0;
        left: auto;
        transform: translateY(-10px);
        min-width: 180px;
    }

    .login-dropdown:hover .dropdown-content {
        transform: translateY(0);
    }

    .dropdown-content::before {
        right: 20px;
        left: auto;
        transform: none;
    }
}

@media (max-width: 480px) {
    .navigation {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .logo {
        top: 0.75rem;
        left: 0.75rem;
    }

    .custom-image {
        top: 0.75rem;
        right: 0.75rem;
        gap: 0.4rem;
    }

    .custom-image img {
        width: 24px;
        height: 24px;
        min-width: 32px;
        min-height: 32px;
    }

    .search-bar {
        height: 42px;
        padding: 0.3rem;
        margin-top: 2.5rem;
        max-width: 100%;
    }

    .search-bar input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .search-bar img {
        width: 18px;
        height: 18px;
    }

    .dropdown-content {
        min-width: 160px;
    }

    .dropdown-content a {
        font-size: 0.85rem;
        padding: 0.6rem 1rem;
    }
}

@media (max-width: 360px) {
    .navigation {
        padding: 0.5rem;
    }

    .logo {
        top: 0.5rem;
        left: 0.5rem;
    }

    .custom-image {
        top: 0.5rem;
        right: 0.5rem;
        gap: 0.3rem;
    }

    .custom-image img {
        width: 20px;
        height: 20px;
        min-width: 28px;
        min-height: 28px;
    }

    .search-bar {
        height: 40px;
        margin-top: 2rem;
        max-width: 100%;
    }

    .search-bar input {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
    }

    .search-bar img {
        width: 16px;
        height: 16px;
    }

    .dropdown-content {
        min-width: 140px;
    }

    .dropdown-content a {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }
}

/* Category Menu Styles */
.menu-container {
    position: relative;
    background: rgb(255, 255, 255);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 1rem;
    background-color: #f2f2f2;
    border-radius: 0;
    width: -moz-fit-content;
    width: fit-content;
    margin: 0 auto;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.category-menu {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 1rem;
    background-color: #ffffff;
    justify-content: center;
    margin: 0;
    flex-direction: row;
}

.menu-item {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #000000;
    font-size: 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    position: relative;
    gap: 0.5rem;
}

.menu-item img {
    width: 24px;
    height: 24px;
    transition: transform 0.3s ease;
}

.menu-item .chevron {
    display: none;
    width: 20px;
    height: 20px;
    margin-left: auto;
}

/* Desktop specific styles */
@media (min-width: 769px) {
    .hamburger {
        display: none !important;
    }

    .category-menu {
        display: flex !important;
        flex-direction: row !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        background-color: #ffffff !important;
        padding: 1rem !important;
        margin: 0 !important;
        overflow: visible !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    }

    .menu-content {
        display: contents !important;
        background: none !important;
        margin: 0 !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        max-height: none !important;
        width: auto !important;
        overflow: visible !important;
    }

    .menu-item {
        padding: 0.5rem 1rem !important;
        border: none !important;
        border-radius: 8px !important;
        background: transparent !important;
        margin: 0 !important;
        width: auto !important;
        flex-direction: row !important;
        gap: 0.5rem !important;
    }

    .menu-item .chevron {
        display: none !important;
    }

    .menu-item::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 50% !important;
        width: 0 !important;
        height: 2px !important;
        background-color: #832729 !important;
        transition: all 0.3s ease !important;
        transform: translateX(-50%) !important;
        display: block !important;
    }

    .menu-item:hover::after {
        width: 80% !important;
    }

    .menu-item:hover {
        background-color: transparent !important;
        padding-left: 1rem !important;
        transform: none !important;
    }
}

.menu-item:hover {
    color: #832729;
}

.menu-item:hover img {
    transform: scale(1.1);
}

.menu-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #832729;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.menu-item:hover::after {
    width: 80%;
}

.menu-item:active {
    transform: translateY(0);
}

/* Mobile Category Menu Styles */
@media (max-width: 768px) {
    .menu-container {
        position: relative;
    }

    .hamburger {
        display: flex;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1005;
        padding: 0.5rem;
        background-color: rgb(255, 255, 255);
        border-radius: 4px;
        width: auto;
        margin: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .hamburger span {
        width: 18px;
        height: 2px;
        background-color: #000000;
        margin: 2px 0;
        transition: 0.3s;
        border-radius: 1px;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-3px, 4px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-3px, -4px);
    }

    /* Adjust logo positioning to make room for hamburger */
    .logo {
        left: 3.5rem !important; /* Move logo to the right to make space for hamburger */
    }

    .category-menu {
        display: none;
        flex-direction: column;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.3) 0%,
            rgba(0, 0, 0, 0.5) 20%,
            rgba(0, 0, 0, 0.5) 80%,
            rgba(0, 0, 0, 0.974) 100%);
        z-index: 1004;
        gap: 0;
        padding: 0;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .category-menu::-webkit-scrollbar {
        display: none;
    }

    .category-menu.active {
        display: flex;
    }

    .menu-content {
        background-color: #ffffff;
        margin: 6vh auto 16vh auto;
        border-radius: 20px;
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-height: 80vh;
        width: 85%;
        max-width: 400px;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .menu-content::-webkit-scrollbar {
        display: none;
    }

    .menu-item {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        justify-content: flex-start;
        gap: 1rem;
        border-radius: 0;
        background-color: transparent;
        margin: 0;
        width: 100%;
        box-sizing: border-box;
        transition: all 0.3s ease;
        color: #000000;
        font-weight: 500;
    }

    .menu-item:first-child {
        border-top: none;
        border-radius: 20px 20px 0 0;
    }

    .menu-item:last-child {
        border-bottom: none;
        border-radius: 0 0 20px 20px;
    }

    .menu-item .chevron {
        display: block;
        opacity: 0.7;
        transition: all 0.3s ease;
    }

    .menu-item::after {
        display: none;
    }

    .menu-item:hover {
        background-color: rgba(247, 184, 26, 0.27);
        transform: none;
        padding-left: 2.5rem;
    }

    .menu-item:hover .chevron {
        opacity: 1;
        transform: translateX(5px);
    }

    .menu-item:active {
        background-color: rgba(131, 39, 41, 0.2);
        transform: scale(0.98);
    }
}

@media (max-width: 480px) {
    .hamburger {
        top: 0.75rem;
        left: 0.75rem;
        padding: 0.4rem;
    }

    .hamburger span {
        width: 16px;
        height: 2px;
        margin: 1.5px 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-2px, 3px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-2px, -3px);
    }

    /* Adjust logo positioning for smaller screens */
    .logo {
        left: 3rem !important;
    }
}

@media (max-width: 360px) {
    .hamburger {
        top: 0.5rem;
        left: 0.5rem;
        padding: 0.3rem;
    }

    .hamburger span {
        width: 14px;
        height: 1.5px;
        margin: 1px 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-2px, 2px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-2px, -2px);
    }

    /* Adjust logo positioning for smallest screens */
    .logo {
        left: 2.5rem !important;
    }
}

