'use client';

import React from 'react';
import '../style/collection.css';

interface CollectionItem {
  id: number;
  src: string;
  alt: string;
  href: string;
  title: string;
  subtitle?: string;
}

const Collection: React.FC = () => {
  const collectionItems: CollectionItem[] = [
    {
      id: 1,
      src: "/main-images/image-1.png",
      alt: "Sparkling Avenues",
      href: "#collection1",
      title: "Sparkling Avenues",
      subtitle: "Diamond Collection"
    },
    {
      id: 2,
      src: "/main-images/image-2.png",
      alt: "Stunning Every Ear",
      href: "#collection2",
      title: "Stunning Every Ear",
      subtitle: "Earrings Collection"
    },
    {
      id: 3,
      src: "/main-images/image-3.png",
      alt: "Daily Wear",
      href: "#collection3",
      title: "Daily Wear",
      subtitle: "Everyday Elegance"
    },
    {
      id: 4,
      src: "/main-images/image-4.png",
      alt: "Gold Collection",
      href: "#collection4",
      title: "Gold Collection",
      subtitle: "Traditional & Modern"
    },
    {
      id: 5,
      src: "/main-images/image-5.png",
      alt: "Wedding Collection",
      href: "#collection5",
      title: "Wedding Collection",
      subtitle: "Bridal Jewelry"
    }
  ];

  const handleCollectionClick = (href: string) => {
    // Handle navigation logic here
    console.log(`Navigating to: ${href}`);
    // You can replace this with your routing logic
  };

  return (
    <div className="collections">
      <h1 id="header">Radhe-Radhe collections</h1>
      <h2 id="subtitle">Shop the latest collections from our exclusive brands</h2>

      {/* Desktop and Tablet Grid Layout */}
      <div className="desktop-layout d-none d-md-block">
        <div className="parent">
          {collectionItems.map((item) => (
            <div key={item.id} className={`div${item.id}`}>
              <a
                href={item.href}
                onClick={(e) => {
                  e.preventDefault();
                  handleCollectionClick(item.href);
                }}
              >
                <img
                  src={item.src}
                  alt={item.alt}
                  id={`image-${item.id}`}
                  className="img-fluid"
                />
              </a>
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="mobile-layout d-block d-md-none">
        {collectionItems.map((item) => (
          <div key={`mobile-${item.id}`} className="position-relative w-100">
            <a
              href={item.href}
              onClick={(e) => {
                e.preventDefault();
                handleCollectionClick(item.href);
              }}
              className="mobile-collection-link"
            >
              <img
                loading="lazy"
                alt={item.alt}
                className="img-fluid w-100"
                src={item.src}
              />
              <div className="mobile-overlay">
                <div className="mobile-text-content">
                  <h3 className="mobile-title">{item.title}</h3>
                  {item.subtitle && (
                    <p className="mobile-subtitle">{item.subtitle}</p>
                  )}
                </div>
              </div>
            </a>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Collection;