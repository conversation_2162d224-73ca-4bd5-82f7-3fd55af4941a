/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Cjwellery-site%5C%5Csrc%5C%5Ccomponents%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Cjwellery-site%5C%5Csrc%5C%5Ccomponents%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/banner.tsx */ \"(app-pages-browser)/./src/components/banner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcGV3cGV3JTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDV29yayU1QkltcG9ydGFudCU1RCU1QyU1Q2p3ZWxsZXJ5LXNpdGUlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYmFubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBbUsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxwZXdwZXdcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxXb3JrW0ltcG9ydGFudF1cXFxcandlbGxlcnktc2l0ZVxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxiYW5uZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Cjwellery-site%5C%5Csrc%5C%5Ccomponents%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxnQkFBZ0I7QUFDcEIsSUFBSSxjQUFjO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwZXdwZXdcXE9uZURyaXZlXFxEZXNrdG9wXFxXb3JrW0ltcG9ydGFudF1cXGp3ZWxsZXJ5LXNpdGVcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxjanNcXHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIFJlYWN0XG4gKiByZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanNcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cblwidXNlIHN0cmljdFwiO1xuXCJwcm9kdWN0aW9uXCIgIT09IHByb2Nlc3MuZW52Lk5PREVfRU5WICYmXG4gIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgICAgIGlmIChudWxsID09IHR5cGUpIHJldHVybiBudWxsO1xuICAgICAgaWYgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHJldHVybiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9DTElFTlRfUkVGRVJFTkNFXG4gICAgICAgICAgPyBudWxsXG4gICAgICAgICAgOiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICAgICAgaWYgKFwic3RyaW5nXCIgPT09IHR5cGVvZiB0eXBlKSByZXR1cm4gdHlwZTtcbiAgICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgICBjYXNlIFJFQUNUX0ZSQUdNRU5UX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiRnJhZ21lbnRcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlByb2ZpbGVyXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1RSSUNUX01PREVfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdHJpY3RNb2RlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJTdXNwZW5zZUxpc3RcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9BQ1RJVklUWV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkFjdGl2aXR5XCI7XG4gICAgICB9XG4gICAgICBpZiAoXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUpXG4gICAgICAgIHN3aXRjaCAoXG4gICAgICAgICAgKFwibnVtYmVyXCIgPT09IHR5cGVvZiB0eXBlLnRhZyAmJlxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWNlaXZlZCBhbiB1bmV4cGVjdGVkIG9iamVjdCBpbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoKS4gVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLlwiXG4gICAgICAgICAgICApLFxuICAgICAgICAgIHR5cGUuJCR0eXBlb2YpXG4gICAgICAgICkge1xuICAgICAgICAgIGNhc2UgUkVBQ1RfUE9SVEFMX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gXCJQb3J0YWxcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlRFWFRfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiB0eXBlLmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OU1VNRVJfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAodHlwZS5fY29udGV4dC5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIikgKyBcIi5Db25zdW1lclwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRTpcbiAgICAgICAgICAgIHZhciBpbm5lclR5cGUgPSB0eXBlLnJlbmRlcjtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lO1xuICAgICAgICAgICAgdHlwZSB8fFxuICAgICAgICAgICAgICAoKHR5cGUgPSBpbm5lclR5cGUuZGlzcGxheU5hbWUgfHwgaW5uZXJUeXBlLm5hbWUgfHwgXCJcIiksXG4gICAgICAgICAgICAgICh0eXBlID0gXCJcIiAhPT0gdHlwZSA/IFwiRm9yd2FyZFJlZihcIiArIHR5cGUgKyBcIilcIiA6IFwiRm9yd2FyZFJlZlwiKSk7XG4gICAgICAgICAgICByZXR1cm4gdHlwZTtcbiAgICAgICAgICBjYXNlIFJFQUNUX01FTU9fVFlQRTpcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIChpbm5lclR5cGUgPSB0eXBlLmRpc3BsYXlOYW1lIHx8IG51bGwpLFxuICAgICAgICAgICAgICBudWxsICE9PSBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA/IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgIDogZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUudHlwZSkgfHwgXCJNZW1vXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgICAgICBpbm5lclR5cGUgPSB0eXBlLl9wYXlsb2FkO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuX2luaXQ7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUoaW5uZXJUeXBlKSk7XG4gICAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICByZXR1cm4gXCJcIiArIHZhbHVlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBjaGVja0tleVN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gICAgICB0cnkge1xuICAgICAgICB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITE7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICEwO1xuICAgICAgfVxuICAgICAgaWYgKEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSBjb25zb2xlO1xuICAgICAgICB2YXIgSlNDb21waWxlcl90ZW1wX2NvbnN0ID0gSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LmVycm9yO1xuICAgICAgICB2YXIgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwID1cbiAgICAgICAgICAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgU3ltYm9sICYmXG4gICAgICAgICAgICBTeW1ib2wudG9TdHJpbmdUYWcgJiZcbiAgICAgICAgICAgIHZhbHVlW1N5bWJvbC50b1N0cmluZ1RhZ10pIHx8XG4gICAgICAgICAgdmFsdWUuY29uc3RydWN0b3IubmFtZSB8fFxuICAgICAgICAgIFwiT2JqZWN0XCI7XG4gICAgICAgIEpTQ29tcGlsZXJfdGVtcF9jb25zdC5jYWxsKFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCxcbiAgICAgICAgICBcIlRoZSBwcm92aWRlZCBrZXkgaXMgYW4gdW5zdXBwb3J0ZWQgdHlwZSAlcy4gVGhpcyB2YWx1ZSBtdXN0IGJlIGNvZXJjZWQgdG8gYSBzdHJpbmcgYmVmb3JlIHVzaW5nIGl0IGhlcmUuXCIsXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0JGpzY29tcCQwXG4gICAgICAgICk7XG4gICAgICAgIHJldHVybiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRUYXNrTmFtZSh0eXBlKSB7XG4gICAgICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSkgcmV0dXJuIFwiPD5cIjtcbiAgICAgIGlmIChcbiAgICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIHR5cGUgJiZcbiAgICAgICAgbnVsbCAhPT0gdHlwZSAmJlxuICAgICAgICB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9MQVpZX1RZUEVcbiAgICAgIClcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIHRyeSB7XG4gICAgICAgIHZhciBuYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICByZXR1cm4gbmFtZSA/IFwiPFwiICsgbmFtZSArIFwiPlwiIDogXCI8Li4uPlwiO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgfVxuICAgIH1cbiAgICBmdW5jdGlvbiBnZXRPd25lcigpIHtcbiAgICAgIHZhciBkaXNwYXRjaGVyID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuQTtcbiAgICAgIHJldHVybiBudWxsID09PSBkaXNwYXRjaGVyID8gbnVsbCA6IGRpc3BhdGNoZXIuZ2V0T3duZXIoKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gVW5rbm93bk93bmVyKCkge1xuICAgICAgcmV0dXJuIEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBoYXNWYWxpZEtleShjb25maWcpIHtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgdmFyIGdldHRlciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoY29uZmlnLCBcImtleVwiKS5nZXQ7XG4gICAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSByZXR1cm4gITE7XG4gICAgICB9XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb25maWcua2V5O1xuICAgIH1cbiAgICBmdW5jdGlvbiBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpIHtcbiAgICAgIGZ1bmN0aW9uIHdhcm5BYm91dEFjY2Vzc2luZ0tleSgpIHtcbiAgICAgICAgc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gfHxcbiAgICAgICAgICAoKHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duID0gITApLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICBcIiVzOiBga2V5YCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0IGluIGB1bmRlZmluZWRgIGJlaW5nIHJldHVybmVkLiBJZiB5b3UgbmVlZCB0byBhY2Nlc3MgdGhlIHNhbWUgdmFsdWUgd2l0aGluIHRoZSBjaGlsZCBjb21wb25lbnQsIHlvdSBzaG91bGQgcGFzcyBpdCBhcyBhIGRpZmZlcmVudCBwcm9wLiAoaHR0cHM6Ly9yZWFjdC5kZXYvbGluay9zcGVjaWFsLXByb3BzKVwiLFxuICAgICAgICAgICAgZGlzcGxheU5hbWVcbiAgICAgICAgICApKTtcbiAgICAgIH1cbiAgICAgIHdhcm5BYm91dEFjY2Vzc2luZ0tleS5pc1JlYWN0V2FybmluZyA9ICEwO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHByb3BzLCBcImtleVwiLCB7XG4gICAgICAgIGdldDogd2FybkFib3V0QWNjZXNzaW5nS2V5LFxuICAgICAgICBjb25maWd1cmFibGU6ICEwXG4gICAgICB9KTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmcoKSB7XG4gICAgICB2YXIgY29tcG9uZW50TmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0aGlzLnR5cGUpO1xuICAgICAgZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSB8fFxuICAgICAgICAoKGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gPSAhMCksXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgXCJBY2Nlc3NpbmcgZWxlbWVudC5yZWYgd2FzIHJlbW92ZWQgaW4gUmVhY3QgMTkuIHJlZiBpcyBub3cgYSByZWd1bGFyIHByb3AuIEl0IHdpbGwgYmUgcmVtb3ZlZCBmcm9tIHRoZSBKU1ggRWxlbWVudCB0eXBlIGluIGEgZnV0dXJlIHJlbGVhc2UuXCJcbiAgICAgICAgKSk7XG4gICAgICBjb21wb25lbnROYW1lID0gdGhpcy5wcm9wcy5yZWY7XG4gICAgICByZXR1cm4gdm9pZCAwICE9PSBjb21wb25lbnROYW1lID8gY29tcG9uZW50TmFtZSA6IG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFJlYWN0RWxlbWVudChcbiAgICAgIHR5cGUsXG4gICAgICBrZXksXG4gICAgICBzZWxmLFxuICAgICAgc291cmNlLFxuICAgICAgb3duZXIsXG4gICAgICBwcm9wcyxcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHNlbGYgPSBwcm9wcy5yZWY7XG4gICAgICB0eXBlID0ge1xuICAgICAgICAkJHR5cGVvZjogUkVBQ1RfRUxFTUVOVF9UWVBFLFxuICAgICAgICB0eXBlOiB0eXBlLFxuICAgICAgICBrZXk6IGtleSxcbiAgICAgICAgcHJvcHM6IHByb3BzLFxuICAgICAgICBfb3duZXI6IG93bmVyXG4gICAgICB9O1xuICAgICAgbnVsbCAhPT0gKHZvaWQgMCAhPT0gc2VsZiA/IHNlbGYgOiBudWxsKVxuICAgICAgICA/IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7XG4gICAgICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgICAgIGdldDogZWxlbWVudFJlZkdldHRlcldpdGhEZXByZWNhdGlvbldhcm5pbmdcbiAgICAgICAgICB9KVxuICAgICAgICA6IE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcInJlZlwiLCB7IGVudW1lcmFibGU6ICExLCB2YWx1ZTogbnVsbCB9KTtcbiAgICAgIHR5cGUuX3N0b3JlID0ge307XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZS5fc3RvcmUsIFwidmFsaWRhdGVkXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IDBcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnSW5mb1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBudWxsXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1N0YWNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnU3RhY2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnVGFza1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1Rhc2tcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmZyZWV6ZSAmJiAoT2JqZWN0LmZyZWV6ZSh0eXBlLnByb3BzKSwgT2JqZWN0LmZyZWV6ZSh0eXBlKSk7XG4gICAgICByZXR1cm4gdHlwZTtcbiAgICB9XG4gICAgZnVuY3Rpb24ganN4REVWSW1wbChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgdmFyIGNoaWxkcmVuID0gY29uZmlnLmNoaWxkcmVuO1xuICAgICAgaWYgKHZvaWQgMCAhPT0gY2hpbGRyZW4pXG4gICAgICAgIGlmIChpc1N0YXRpY0NoaWxkcmVuKVxuICAgICAgICAgIGlmIChpc0FycmF5SW1wbChjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAoXG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPSAwO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuIDwgY2hpbGRyZW4ubGVuZ3RoO1xuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuKytcbiAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baXNTdGF0aWNDaGlsZHJlbl0pO1xuICAgICAgICAgICAgT2JqZWN0LmZyZWV6ZSAmJiBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICB9IGVsc2VcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVhY3QuanN4OiBTdGF0aWMgY2hpbGRyZW4gc2hvdWxkIGFsd2F5cyBiZSBhbiBhcnJheS4gWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiBVc2UgdGhlIEJhYmVsIHRyYW5zZm9ybSBpbnN0ZWFkLlwiXG4gICAgICAgICAgICApO1xuICAgICAgICBlbHNlIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuKTtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgXCJrZXlcIikpIHtcbiAgICAgICAgY2hpbGRyZW4gPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMoY29uZmlnKS5maWx0ZXIoZnVuY3Rpb24gKGspIHtcbiAgICAgICAgICByZXR1cm4gXCJrZXlcIiAhPT0gaztcbiAgICAgICAgfSk7XG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPVxuICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aFxuICAgICAgICAgICAgPyBcIntrZXk6IHNvbWVLZXksIFwiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCJcbiAgICAgICAgICAgIDogXCJ7a2V5OiBzb21lS2V5fVwiO1xuICAgICAgICBkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSB8fFxuICAgICAgICAgICgoa2V5cyA9XG4gICAgICAgICAgICAwIDwga2V5cy5sZW5ndGggPyBcIntcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiIDogXCJ7fVwiKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgJ0EgcHJvcHMgb2JqZWN0IGNvbnRhaW5pbmcgYSBcImtleVwiIHByb3AgaXMgYmVpbmcgc3ByZWFkIGludG8gSlNYOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMgey4uLnByb3BzfSAvPlxcblJlYWN0IGtleXMgbXVzdCBiZSBwYXNzZWQgZGlyZWN0bHkgdG8gSlNYIHdpdGhvdXQgdXNpbmcgc3ByZWFkOlxcbiAgbGV0IHByb3BzID0gJXM7XFxuICA8JXMga2V5PXtzb21lS2V5fSB7Li4ucHJvcHN9IC8+JyxcbiAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgICAgIGtleXMsXG4gICAgICAgICAgICBjaGlsZHJlblxuICAgICAgICAgICksXG4gICAgICAgICAgKGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dID0gITApKTtcbiAgICAgIH1cbiAgICAgIGNoaWxkcmVuID0gbnVsbDtcbiAgICAgIHZvaWQgMCAhPT0gbWF5YmVLZXkgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24obWF5YmVLZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgbWF5YmVLZXkpKTtcbiAgICAgIGhhc1ZhbGlkS2V5KGNvbmZpZykgJiZcbiAgICAgICAgKGNoZWNrS2V5U3RyaW5nQ29lcmNpb24oY29uZmlnLmtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBjb25maWcua2V5KSk7XG4gICAgICBpZiAoXCJrZXlcIiBpbiBjb25maWcpIHtcbiAgICAgICAgbWF5YmVLZXkgPSB7fTtcbiAgICAgICAgZm9yICh2YXIgcHJvcE5hbWUgaW4gY29uZmlnKVxuICAgICAgICAgIFwia2V5XCIgIT09IHByb3BOYW1lICYmIChtYXliZUtleVtwcm9wTmFtZV0gPSBjb25maWdbcHJvcE5hbWVdKTtcbiAgICAgIH0gZWxzZSBtYXliZUtleSA9IGNvbmZpZztcbiAgICAgIGNoaWxkcmVuICYmXG4gICAgICAgIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKFxuICAgICAgICAgIG1heWJlS2V5LFxuICAgICAgICAgIFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIHR5cGVcbiAgICAgICAgICAgID8gdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgXCJVbmtub3duXCJcbiAgICAgICAgICAgIDogdHlwZVxuICAgICAgICApO1xuICAgICAgcmV0dXJuIFJlYWN0RWxlbWVudChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgZ2V0T3duZXIoKSxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGRlYnVnU3RhY2ssXG4gICAgICAgIGRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gdmFsaWRhdGVDaGlsZEtleXMobm9kZSkge1xuICAgICAgXCJvYmplY3RcIiA9PT0gdHlwZW9mIG5vZGUgJiZcbiAgICAgICAgbnVsbCAhPT0gbm9kZSAmJlxuICAgICAgICBub2RlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUgJiZcbiAgICAgICAgbm9kZS5fc3RvcmUgJiZcbiAgICAgICAgKG5vZGUuX3N0b3JlLnZhbGlkYXRlZCA9IDEpO1xuICAgIH1cbiAgICB2YXIgUmVhY3QgPSByZXF1aXJlKFwibmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0XCIpLFxuICAgICAgUkVBQ1RfRUxFTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnRyYW5zaXRpb25hbC5lbGVtZW50XCIpLFxuICAgICAgUkVBQ1RfUE9SVEFMX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucG9ydGFsXCIpLFxuICAgICAgUkVBQ1RfRlJBR01FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mcmFnbWVudFwiKSxcbiAgICAgIFJFQUNUX1NUUklDVF9NT0RFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3RyaWN0X21vZGVcIiksXG4gICAgICBSRUFDVF9QUk9GSUxFUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnByb2ZpbGVyXCIpLFxuICAgICAgUkVBQ1RfQ09OU1VNRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb25zdW1lclwiKSxcbiAgICAgIFJFQUNUX0NPTlRFWFRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jb250ZXh0XCIpLFxuICAgICAgUkVBQ1RfRk9SV0FSRF9SRUZfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5mb3J3YXJkX3JlZlwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3Quc3VzcGVuc2VfbGlzdFwiKSxcbiAgICAgIFJFQUNUX01FTU9fVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5tZW1vXCIpLFxuICAgICAgUkVBQ1RfTEFaWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmxhenlcIiksXG4gICAgICBSRUFDVF9BQ1RJVklUWV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmFjdGl2aXR5XCIpLFxuICAgICAgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5jbGllbnQucmVmZXJlbmNlXCIpLFxuICAgICAgUmVhY3RTaGFyZWRJbnRlcm5hbHMgPVxuICAgICAgICBSZWFjdC5fX0NMSUVOVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9XQVJOX1VTRVJTX1RIRVlfQ0FOTk9UX1VQR1JBREUsXG4gICAgICBoYXNPd25Qcm9wZXJ0eSA9IE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHksXG4gICAgICBpc0FycmF5SW1wbCA9IEFycmF5LmlzQXJyYXksXG4gICAgICBjcmVhdGVUYXNrID0gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgID8gY29uc29sZS5jcmVhdGVUYXNrXG4gICAgICAgIDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfTtcbiAgICBSZWFjdCA9IHtcbiAgICAgIHJlYWN0X3N0YWNrX2JvdHRvbV9mcmFtZTogZnVuY3Rpb24gKGNhbGxTdGFja0ZvckVycm9yKSB7XG4gICAgICAgIHJldHVybiBjYWxsU3RhY2tGb3JFcnJvcigpO1xuICAgICAgfVxuICAgIH07XG4gICAgdmFyIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duO1xuICAgIHZhciBkaWRXYXJuQWJvdXRFbGVtZW50UmVmID0ge307XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnU3RhY2sgPSBSZWFjdC5yZWFjdF9zdGFja19ib3R0b21fZnJhbWUuYmluZChcbiAgICAgIFJlYWN0LFxuICAgICAgVW5rbm93bk93bmVyXG4gICAgKSgpO1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1Rhc2sgPSBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKFVua25vd25Pd25lcikpO1xuICAgIHZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbiAgICBleHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbiAgICBleHBvcnRzLmpzeERFViA9IGZ1bmN0aW9uIChcbiAgICAgIHR5cGUsXG4gICAgICBjb25maWcsXG4gICAgICBtYXliZUtleSxcbiAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICBzb3VyY2UsXG4gICAgICBzZWxmXG4gICAgKSB7XG4gICAgICB2YXIgdHJhY2tBY3R1YWxPd25lciA9XG4gICAgICAgIDFlNCA+IFJlYWN0U2hhcmVkSW50ZXJuYWxzLnJlY2VudGx5Q3JlYXRlZE93bmVyU3RhY2tzKys7XG4gICAgICByZXR1cm4ganN4REVWSW1wbChcbiAgICAgICAgdHlwZSxcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBzZWxmLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyXG4gICAgICAgICAgPyBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKVxuICAgICAgICAgIDogdW5rbm93bk93bmVyRGVidWdTdGFjayxcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lciA/IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUodHlwZSkpIDogdW5rbm93bk93bmVyRGVidWdUYXNrXG4gICAgICApO1xuICAgIH07XG4gIH0pKCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGV3cGV3XFxPbmVEcml2ZVxcRGVza3RvcFxcV29ya1tJbXBvcnRhbnRdXFxqd2VsbGVyeS1zaXRlXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/banner.tsx":
/*!***********************************!*\
  !*** ./src/components/banner.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _style_banner_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../style/banner.css */ \"(app-pages-browser)/./src/style/banner.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Banner() {\n    _s();\n    const [slideIndex, setSlideIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const totalSlides = 4;\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const autoSlideInterval = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const startX = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const endX = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const changeSlide = (direction)=>{\n        setIsTransitioning(true);\n        setSlideIndex((prevIndex)=>{\n            if (direction > 0) {\n                // Moving forward\n                return prevIndex + 1;\n            } else {\n                // Moving backward\n                return prevIndex - 1;\n            }\n        });\n        restartAutoSlide();\n    };\n    const currentSlide = (index)=>{\n        setIsTransitioning(true);\n        setSlideIndex(index);\n        restartAutoSlide();\n    };\n    const handleTransitionEnd = ()=>{\n        if (slideIndex === totalSlides + 1) {\n            // After sliding to duplicate first slide, instantly jump to real first slide\n            setIsTransitioning(false);\n            setTimeout(()=>setSlideIndex(1), 10);\n        } else if (slideIndex === 0) {\n            // After sliding to duplicate last slide, instantly jump to real last slide\n            setIsTransitioning(false);\n            setTimeout(()=>setSlideIndex(totalSlides), 10);\n        }\n    };\n    const handleImageClick = (imageNumber)=>{\n        stopAutoSlide();\n        switch(imageNumber){\n            case 1:\n                alert('Banner 1 clicked! You can redirect to a specific page or product.');\n                break;\n            case 2:\n                alert('Banner 2 clicked! Add your custom action here.');\n                break;\n            case 3:\n                alert('Banner 3 clicked! Add your custom action here.');\n                break;\n            case 4:\n                alert('Banner 4 clicked! Add your custom action here.');\n                break;\n            default:\n                console.log('Image clicked:', imageNumber);\n        }\n        setTimeout(startAutoSlide, 3000);\n    };\n    const autoSlide = ()=>{\n        changeSlide(1);\n    };\n    const startAutoSlide = ()=>{\n        if (autoSlideInterval.current) {\n            clearInterval(autoSlideInterval.current);\n        }\n        autoSlideInterval.current = setInterval(autoSlide, 4000);\n    };\n    const stopAutoSlide = ()=>{\n        if (autoSlideInterval.current) {\n            clearInterval(autoSlideInterval.current);\n            autoSlideInterval.current = null;\n        }\n    };\n    const restartAutoSlide = ()=>{\n        stopAutoSlide();\n        startAutoSlide();\n    };\n    const handleKeyDown = (event)=>{\n        if (event.key === 'ArrowLeft') {\n            changeSlide(-1);\n        } else if (event.key === 'ArrowRight') {\n            changeSlide(1);\n        }\n    };\n    const handleTouchStart = (event)=>{\n        startX.current = event.touches[0].clientX;\n    };\n    const handleTouchEnd = (event)=>{\n        endX.current = event.changedTouches[0].clientX;\n        handleSwipe();\n    };\n    const handleSwipe = ()=>{\n        const swipeThreshold = 50;\n        const diff = startX.current - endX.current;\n        if (Math.abs(diff) > swipeThreshold) {\n            if (diff > 0) {\n                changeSlide(1);\n            } else {\n                changeSlide(-1);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Banner.useEffect\": ()=>{\n            // Check if mobile on mount and window resize\n            const checkMobile = {\n                \"Banner.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth <= 768);\n                }\n            }[\"Banner.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            startAutoSlide();\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Banner.useEffect\": ()=>{\n                    stopAutoSlide();\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', checkMobile);\n                }\n            })[\"Banner.useEffect\"];\n        }\n    }[\"Banner.useEffect\"], []);\n    // Handle the instant repositioning after transition ends\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Banner.useEffect\": ()=>{\n            if (!isTransitioning) {\n                const timer = setTimeout({\n                    \"Banner.useEffect.timer\": ()=>{\n                        setIsTransitioning(true);\n                    }\n                }[\"Banner.useEffect.timer\"], 50);\n                return ({\n                    \"Banner.useEffect\": ()=>clearTimeout(timer)\n                })[\"Banner.useEffect\"];\n            }\n        }\n    }[\"Banner.useEffect\"], [\n        isTransitioning\n    ]);\n    // Function to get appropriate image source based on screen size\n    const getImageSrc = (imageNumber)=>{\n        const prefix = isMobile ? 'small-banner' : 'banner-image';\n        return \"/banners/\".concat(prefix, \"-\").concat(imageNumber, \".png\");\n    };\n    // Calculate translateX for infinite loop (6 slides total: last + 4 original + first)\n    // slideIndex 1 = real first slide (position 1 in the array)\n    const translateX = -slideIndex * 16.666667;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"slideshow-container\",\n        onTouchStart: handleTouchStart,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"slides-wrapper\",\n                style: {\n                    transform: \"translateX(\".concat(translateX, \"%)\"),\n                    transition: isTransitioning ? 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'none'\n                },\n                onTransitionEnd: handleTransitionEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(4),\n                            alt: \"Banner 4\",\n                            onClick: ()=>handleImageClick(4)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(1),\n                            alt: \"Banner 1\",\n                            onClick: ()=>handleImageClick(1)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(2),\n                            alt: \"Banner 2\",\n                            onClick: ()=>handleImageClick(2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(3),\n                            alt: \"Banner 3\",\n                            onClick: ()=>handleImageClick(3)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(4),\n                            alt: \"Banner 4\",\n                            onClick: ()=>handleImageClick(4)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"slide\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: getImageSrc(1),\n                            alt: \"Banner 1\",\n                            onClick: ()=>handleImageClick(1)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"nav-button prev\",\n                onClick: ()=>changeSlide(-1),\n                children: \"❮\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"nav-button next\",\n                onClick: ()=>changeSlide(1),\n                children: \"❯\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dots-container\",\n                children: [\n                    1,\n                    2,\n                    3,\n                    4\n                ].map((index)=>{\n                    // Calculate which dot should be active based on current slide position\n                    let activeIndex = slideIndex;\n                    if (slideIndex === 0) activeIndex = 4; // When at duplicate last slide\n                    if (slideIndex === totalSlides + 1) activeIndex = 1; // When at duplicate first slide\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"dot \".concat(activeIndex === index ? 'active' : ''),\n                        onClick: ()=>currentSlide(index)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Work[Important]\\\\jwellery-site\\\\src\\\\components\\\\banner.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(Banner, \"+eEyuFsruMo3pJUFc6iPvoNxfJk=\");\n_c = Banner;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Banner);\nvar _c;\n$RefreshReg$(_c, \"Banner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/banner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/style/banner.css":
/*!******************************!*\
  !*** ./src/style/banner.css ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0fc7058734f7\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZS9iYW5uZXIuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwZXdwZXdcXE9uZURyaXZlXFxEZXNrdG9wXFxXb3JrW0ltcG9ydGFudF1cXGp3ZWxsZXJ5LXNpdGVcXHNyY1xcc3R5bGVcXGJhbm5lci5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZmM3MDU4NzM0ZjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/style/banner.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpewpew%5C%5COneDrive%5C%5CDesktop%5C%5CWork%5BImportant%5D%5C%5Cjwellery-site%5C%5Csrc%5C%5Ccomponents%5C%5Cbanner.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);