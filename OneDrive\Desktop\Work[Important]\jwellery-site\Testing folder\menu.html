<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>menu</title>
</head>

<body>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .menu-container {
            position: relative;
        }

        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 1rem;
            background-color: #f2f2f2;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: fit-content;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background-color: #333;
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .hamburger.active span:nth-child(1) {
            transform: rotate(-45deg) translate(-5px, 6px);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active span:nth-child(3) {
            transform: rotate(45deg) translate(-5px, -6px);
        }

        .Menu {
            display: flex;
            flex-wrap: wrap;
            gap: 1.5rem;
            padding: 1rem;
            background-color: #f2f2f2;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            justify-content: center;
        }

        .menu-item {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #333;
            font-size: 1.2rem;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            position: relative;
            gap: 0.5rem;
        }

        .menu-item img {
            width: 24px;
            height: 24px;
            transition: transform 0.3s ease;
        }

        .menu-item .chevron {
            display: none;
            width: 20px;
            height: 20px;
            margin-left: auto;
        }

        .menu-item:hover {
            color: #832729;
        }

        .menu-item:hover img {
            transform: scale(1.1);
        }

        .menu-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background-color: #832729;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .menu-item:hover::after {
            width: 80%;
        }

        .menu-item:active {
            transform: translateY(0);
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            .Menu {
                display: none;
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background-color: #f2f2f2;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                margin-top: 0.5rem;
                gap: 0;
            }

            .Menu.active {
                display: flex;
            }

            .menu-item {
                padding: 1rem;
                border-bottom: 1px solid #ddd;
                justify-content: flex-start;
                gap: 1rem;
            }

            .menu-item:last-child {
                border-bottom: none;
            }

            .menu-item .chevron {
                display: block;
                
            }

            .menu-item::after {
                display: none;
            }

            .menu-item:hover {
                background-color: rgba(131, 39, 41, 0.1);
                transform: none;
            }
        }
    </style>

    <div class="menu-container">
        <div class="hamburger" onclick="toggleMenu()">
            <span></span>
            <span></span>
            <span></span>
        </div>

        <div class="Menu" id="menu">
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-jewelry-50.png" alt="Jewelry">
                All Jewelerry
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-gold-50.png" alt="Gold">
                Gold
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-diamond-50 (1).png" alt="Diamond">
                Diamond
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-earrings-50.png" alt="Earrings">
                Earrings
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-diamond-ring-50.png" alt="Rings">
                Rings
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-collection-50.png" alt="Collections">
                Collections
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-wedding-50.png" alt="Wedding">
                Wedding
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
            <a href="#" class="menu-item">
                <img src="../public/images/icons8-gift-50.png" alt="Gifting">
                Gifting
                <img src="../public/images/icons8-chevron-30.png" alt="Chevron" class="chevron">
            </a>
        </div>
    </div>

    <script>
        function toggleMenu() {
            const hamburger = document.querySelector('.hamburger');
            const menu = document.getElementById('menu');

            hamburger.classList.toggle('active');
            menu.classList.toggle('active');
        }

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            const menuContainer = document.querySelector('.menu-container');
            const hamburger = document.querySelector('.hamburger');
            const menu = document.getElementById('menu');

            if (!menuContainer.contains(event.target)) {
                hamburger.classList.remove('active');
                menu.classList.remove('active');
            }
        });

        // Handle Enter key for accessibility
        document.querySelector('.hamburger').addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                toggleMenu();
            }
        });
    </script>
</body>

</html>