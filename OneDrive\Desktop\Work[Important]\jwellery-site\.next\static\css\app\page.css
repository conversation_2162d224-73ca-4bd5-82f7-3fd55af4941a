/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/style/banner.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* Banner Slideshow Styles */

.slideshow-container {
    position: relative;
    width: 100vw;
    margin: 0;
    overflow: hidden;
}

.slides-wrapper {
    display: flex;
    width: 600%; /* 6 slides × 100% = 600% (4 original + 2 duplicates) */
    height: 600px;
}

.slide {
    width: 16.666667%; /* Each slide takes 16.666667% of the wrapper (100% / 6 slides) */
    height: 100%;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.nav-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 24px;
    color: #333;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-button:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.nav-button:active {
    transform: translateY(-50%) scale(0.95);
}

.prev {
    left: 20px;
}

.next {
    right: 20px;
}

.dots-container {
    text-align: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
}

.dot {
    height: 15px;
    width: 15px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot:hover {
    background-color: #717171;
    transform: scale(1.2);
}

.dot.active {
    background-color: #667eea;
    transform: scale(1.3);
}



@media (max-width: 768px) {
    .slideshow-container {
        width: 100%;
    }

    .slides-wrapper {
        height: 450px; /* Increased height to accommodate full image */
    }

    .slide img {
        object-fit: contain; /* Show full image without cropping */
        object-position: center;
        background-color: #ffffff; /* White background for any letterboxing */
        width: 100%;
        height: 100%;
    }

    /* Hide navigation buttons on mobile */
    .nav-button {
        display: none;
    }

    /* Hide dots container on mobile */
    .dots-container {
        display: none;
    }
}

/* Additional breakpoint for very small screens */
@media (max-width: 480px) {
    .slides-wrapper {
        height: 400px; /* Increased height for small screens */
    }

    .slide img {
        object-fit: contain; /* Ensure full image is visible */
        object-position: center;
    }
}
